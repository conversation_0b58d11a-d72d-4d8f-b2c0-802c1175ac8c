use anyhow::{Context, Ok, Result, anyhow};
use lru::LruCache;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use serenity::all::{Channel, ChannelId, EditChannel, Http};
use std::{fmt::Debug, num::NonZeroUsize, sync::Arc};
use tokio::sync::RwLock;
use tracing::debug;

#[derive(Debug)]
pub struct Storage {
    http: Arc<Http>,
    records: RwLock<LruCache<ChannelId, Value>>,
}

impl Storage {
    pub fn new(http: Arc<Http>, lru_capacity: usize) -> Self {
        Self {
            http: http,
            records: RwLock::new(LruCache::new(
                NonZeroUsize::new(lru_capacity)
                    .expect("failed to create lru cache due zero capacity value"),
            )),
        }
    }

    /// fetches the record for the given channel id.
    ///
    /// # Arguments
    ///
    /// * `id` - the channel id of the record.
    ///
    /// # Errors
    ///
    /// `anyhow::Error` - if the service fails to fetch the record due to an error such as the channel not existing.
    ///
    /// # Returns
    ///
    /// `Option<T>` - the record for the given channel id.
    ///
    pub async fn read<T>(&self, id: &ChannelId) -> Result<Option<T>>
    where
        T: Serialize + for<'de> Deserialize<'de> + Debug,
    {
        debug!("record fetch; channel_id={}", id);

        if let Some(record) = self.records.read().await.peek(&id).cloned() {
            debug!("cache hit; channel_id={} record={:#?}", id, record);

            return Ok(Some(T::deserialize(record)?));
        }

        debug!("cache miss; channel_id={}", id);

        match self.http.get_channel(*id).await? {
            Channel::Guild(channel) => match channel.topic {
                Some(topic) => {
                    let record = serde_json::from_str::<T>(&topic)
                        .context(format!("failed to deserialize record; channel_id={}", id))?;

                    debug!("record fetched; channel_id={} record={:#?}", id, record);

                    self.records
                        .write()
                        .await
                        .put(*id, serde_json::to_value(&record)?);

                    debug!("cache write; channel_id={} record={:#?}", id, record);

                    Ok(Some(record))
                }
                None => Ok(None),
            },
            _ => Err(anyhow!(
                "failed to fetch channel on record read; channel_id={}",
                id
            )),
        }
    }

    /// writes the record for the given channel id.
    ///
    /// # Arguments
    ///
    /// * `id` - the channel id of the record.
    /// * `record` - the record to write.
    ///
    /// # Errors
    ///
    /// `anyhow::Error` - if the service fails to write the record due to an error such as the channel not existing.
    ///
    /// # Returns
    ///
    /// `T` - the record for the given channel id; the record is returned for convenience.
    ///
    pub async fn write<T>(&self, id: ChannelId, record: T) -> Result<T>
    where
        T: Serialize + for<'de> Deserialize<'de> + Debug,
    {
        match self.http.get_channel(id).await? {
            Channel::Guild(mut channel) => {
                let serialized = serde_json::to_string(&record).context(format!(
                    "failed to serialize record; channel_id={} record={:#?}",
                    id, record
                ))?;

                channel
                    .edit(&self.http, EditChannel::new().topic(&serialized))
                    .await
                    .context(format!(
                        "failed to edit channel topic on record write; channel_id={} record={:#?}",
                        id, serialized
                    ))?;

                if self.records.read().await.contains(&id) {
                    debug!("cache overwrite; channel_id={} record={:#?}", id, record);

                    self.records
                        .write()
                        .await
                        .put(id, serde_json::to_value(&record)?);
                }

                Ok(record)
            }
            _ => Err(anyhow!(
                "failed to fetch channel on record write; channel_id={} record={:#?}",
                id,
                record
            ))?,
        }
    }
}
