use rocket::{State, futures::future::join_all, post, serde::json::<PERSON><PERSON>};
use serde::Deserialize;
use serenity::all::{Channel, CreateAttachment, CreateMessage, UserId};
use tracing::{debug, error, info};

use crate::{
    ENV,
    state::RocketState,
    trengo::{self, models::ContactId},
};

#[derive(Debug, Deserialize)]
pub struct WebhookPayload<'a> {
    message_id: &'a str,
    ticket_id: &'a str,
    contact_identifier: Option<&'a str>,
}

#[post("/webhook", format = "json", data = "<payload>")]
pub async fn handler<'a>(payload: Json<WebhookPayload<'a>>, state: &State<RocketState>) {
    // TODO: verify signature of payload

    if payload.contact_identifier.is_none() {
        return;
    }

    let (user_id, contact_id) = {
        let contact_id = ContactId::new(payload.contact_identifier.unwrap());

        if contact_id.is_none() {
            debug!(
                "recv request for non-custom channel; path=/webhook payload={:#?}",
                payload,
            );

            return;
        }

        let contact_id = contact_id.unwrap();

        let user_id = UserId::from(&contact_id);

        (user_id, contact_id)
    };

    info!(
        "recv request to store message in discord; user_id={}",
        user_id
    );

    let (mut ticket, _ticket_channel) = match state.tickets.get_ticket(&user_id).await {
        Ok(ticket) => match ticket {
            Some(ticket) => ticket,
            None => {
                info!("failed to fetch ticket; user_id={}", user_id);

                if let Err(error) = state.tickets.upsert_ticket(&user_id).await {
                    error!(
                        "failed to create ticket; user_id={} error={:#?}",
                        user_id, error
                    );

                    return;
                }

                let ticket = state.tickets.get_ticket(&user_id).await.unwrap().unwrap();

                ticket
            }
        },
        Err(error) => {
            error!(
                "failed to refresh ticket; user_id={} error={:#?}",
                user_id, error
            );

            return;
        }
    };

    let guild = match state
        .discord_http
        .get_guild(ENV.discord_guild_id.into())
        .await
        .ok()
    {
        Some(guild) => guild,
        None => {
            error!("failed to fetch guild; guild_id={}", ENV.discord_guild_id);

            return;
        }
    };

    if let Some(channels) = guild.channels(&state.discord_http).await.ok() {
        if !channels.contains_key(&ticket.channel_id) {
            let (refreshed_ticket, _refreshed_ticket_channel) =
                match state.tickets.upsert_ticket(&ticket.user_id).await {
                    Ok(ticket) => ticket,
                    Err(error) => {
                        error!(
                            "failed to refresh ticket; user_id={} error={:#?}",
                            ticket.user_id, error
                        );

                        return;
                    }
                };

            ticket = refreshed_ticket;
        }
    }

    let channel = match state.discord_http.get_channel(ticket.channel_id).await.ok() {
        Some(channel) => match channel {
            Channel::Guild(channel) => channel,
            _ => {
                error!(
                    "failed to fetch channel; user_id={} channel_id={}",
                    ticket.user_id, ticket.channel_id
                );

                return;
            }
        },
        None => {
            error!(
                "failed to fetch channel; user_id={} channel_id={}",
                ticket.user_id, ticket.channel_id
            );

            return;
        }
    };

    match trengo::fetch_message(payload.ticket_id, payload.message_id).await {
        Ok(message) => {
            let attachments = join_all(
                message
                    .attachment_urls
                    .into_iter()
                    .map(async |url| CreateAttachment::url(&state.discord_http, &url).await),
            )
            .await
            .into_iter()
            .filter(|attachment| attachment.is_ok())
            .map(|attachment| attachment.unwrap())
            .collect::<Vec<_>>();

            let builder = CreateMessage::new()
                .content(message.content)
                .add_files(attachments);

            match channel.send_message(&state.discord_http, builder).await {
                Ok(message) => {
                    info!(
                        "stored message in discord; user_id={} message={:#?}",
                        ticket.user_id, message
                    );
                }
                Err(why) => {
                    error!(
                        "failed to send message to discord; user_id={} error={:#?}",
                        ticket.user_id, why
                    );
                }
            }
        }
        Err(error) => {
            error!(
                "failed to fetch message from trengo; user_id={} error={:#?}",
                ticket.user_id, error,
            );
        }
    }
}
