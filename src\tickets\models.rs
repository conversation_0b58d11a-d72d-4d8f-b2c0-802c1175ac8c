use std::fmt::Display;

use serde::{Deserialize, Serialize};
use serenity::{
    all::GuildId,
    model::id::{ChannelId, UserId},
};

use crate::trengo::models::{ContactId, ContractName};

#[derive(Debu<PERSON>, <PERSON><PERSON>, Default)]
pub struct Mailbox {
    pub guild_id: GuildId,
    pub category_id: ChannelId,
    pub channel_id: ChannelId,
}

impl Mailbox {
    pub fn new(guild_id: GuildId, category_id: ChannelId, channel_id: ChannelId) -> Self {
        Self {
            guild_id: guild_id,
            category_id: category_id,
            channel_id: channel_id,
        }
    }
}

impl Display for Mailbox {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "mailbox|guild_id={}|category_id={}|channel_id={}",
            self.guild_id, self.category_id, self.channel_id
        )
    }
}

impl Serialize for Mailbox {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        let encoded = format!(
            "mailbox|{}|{}|{}",
            self.guild_id, self.category_id, self.channel_id
        );

        serializer.serialize_str(&encoded)
    }
}

impl<'de> Deserialize<'de> for Mailbox {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let decoded = String::deserialize(deserializer)?;

        let mut parts = decoded.split('|');

        let tag = parts.next().ok_or(serde::de::Error::custom(
            "failed to deserialize mailbox; missing tag",
        ))?;

        if tag != "mailbox" {
            return Err(serde::de::Error::custom(format!(
                "failed to deserialize mailbox; invalid tag; tag={}",
                tag
            )));
        }

        let guild_id = parts.next().ok_or(serde::de::Error::custom(
            "failed to deserialize mailbox; missing guild_id",
        ))?;

        let category_id = parts.next().ok_or(serde::de::Error::custom(
            "failed to deserialize mailbox; missing category_id",
        ))?;

        let channel_id = parts.next().ok_or(serde::de::Error::custom(
            "failed to deserialize mailbox; missing channel_id",
        ))?;

        let guild_id = guild_id.parse::<GuildId>().map_err(|error| {
            serde::de::Error::custom(format!(
                "failed to deserialize mailbox; invalid guild_id; guild_id={} error={:#?}",
                guild_id, error
            ))
        })?;

        let category_id = category_id.parse::<ChannelId>().map_err(|error| {
            serde::de::Error::custom(format!(
                "failed to deserialize mailbox; invalid category_id; category_id={} error={:#?}",
                category_id, error
            ))
        })?;

        let channel_id = channel_id.parse::<ChannelId>().map_err(|error| {
            serde::de::Error::custom(format!(
                "failed to deserialize mailbox; invalid channel_id; channel_id={} error={:#?}",
                channel_id, error
            ))
        })?;

        Ok(Self::new(guild_id, category_id, channel_id))
    }
}

#[derive(Debug, Clone, Default)]
pub struct Ticket {
    pub user_id: UserId,
    pub channel_id: ChannelId,
    pub contact_id: ContactId,
    pub contact_name: ContractName,
}

impl Ticket {
    pub fn new(
        user_id: UserId,
        channel_id: ChannelId,
        contact_id: ContactId,
        contact_name: ContractName,
    ) -> Self {
        Self {
            user_id: user_id,
            channel_id: channel_id,
            contact_id: contact_id,
            contact_name: contact_name,
        }
    }
}

impl Display for Ticket {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "ticket|user_id={}|channel_id={}|contact_id={}|contact_name={}",
            self.user_id, self.channel_id, self.contact_id, self.contact_name
        )
    }
}

impl Serialize for Ticket {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        let encoded = format!(
            "ticket|{}|{}|{}|{}",
            self.user_id, self.channel_id, self.contact_id, self.contact_name
        );

        serializer.serialize_str(&encoded)
    }
}

impl<'de> Deserialize<'de> for Ticket {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let decoded = String::deserialize(deserializer)?;

        let mut parts = decoded.split('|');

        let tag = parts.next().ok_or(serde::de::Error::custom(
            "failed to deserialize ticket; missing tag",
        ))?;

        if tag != "ticket" {
            return Err(serde::de::Error::custom(format!(
                "failed to deserialize ticket; invalid tag; tag={}",
                tag
            )));
        }

        let user_id = parts.next().ok_or(serde::de::Error::custom(
            "failed to deserialize ticket; missing user_id",
        ))?;

        let channel_id = parts.next().ok_or(serde::de::Error::custom(
            "failed to deserialize ticket; missing channel_id",
        ))?;

        let contact_id = parts.next().ok_or(serde::de::Error::custom(
            "failed to deserialize ticket; missing contact_id",
        ))?;

        let contact_name = parts.next().ok_or(serde::de::Error::custom(
            "failed to deserialize ticket; missing contact_name",
        ))?;

        let user_id = user_id.parse::<UserId>().map_err(|error| {
            serde::de::Error::custom(format!(
                "failed to deserialize ticket; invalid user_id; user_id={} error={:#?}",
                user_id, error
            ))
        })?;

        let channel_id = channel_id.parse::<ChannelId>().map_err(|error| {
            serde::de::Error::custom(format!(
                "failed to deserialize ticket; invalid channel_id; channel_id={} error={:#?}",
                channel_id, error
            ))
        })?;

        let contact_id = ContactId::new(&contact_id).ok_or_else(|| {
            serde::de::Error::custom(format!(
                "failed to deserialize ticket; invalid contact_id; contact_id={}",
                contact_id
            ))
        })?;

        let contact_name = ContractName::new(&contact_name);

        Ok(Self::new(user_id, channel_id, contact_id, contact_name))
    }
}
